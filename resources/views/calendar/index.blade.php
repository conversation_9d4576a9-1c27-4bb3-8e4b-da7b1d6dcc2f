@extends('layouts.admin')

@section('title', 'Calendar & Bookings - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Calendar & Bookings</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Calendar</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Calendar Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Field Bookings Calendar</div>
                    <div class="d-flex gap-2 align-items-center">
                        <!-- Field Filter -->
                        <select id="fieldFilter" class="form-select form-select-sm">
                            <option value="">All Fields</option>
                            @foreach ($fields as $field)
                                <option value="{{ $field->id }}">{{ $field->name }}</option>
                            @endforeach
                        </select>

                        <a href="{{ route('bookings.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Booking
                        </a>
                        <a href="{{ route('bookings.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Legend -->
                    <div class="mb-4 p-3 bg-light rounded">
                        <h6 class="fw-semibold mb-2">Legend:</h6>
                        <div class="d-flex flex-wrap gap-3 fs-12">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-warning me-2">&nbsp;</span>
                                <span>Pending</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">&nbsp;</span>
                                <span>Confirmed</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">&nbsp;</span>
                                <span>Completed</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-danger me-2">&nbsp;</span>
                                <span>Cancelled</span>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="calendarLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading calendar...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading calendar events...</p>
                    </div>

                    <!-- Calendar Container -->
                    <div id="calendar" style="display: none;"></div>

                    <!-- Error State -->
                    <div id="calendarError" class="alert alert-danger d-none">
                        <h6 class="fw-semibold">Calendar Error</h6>
                        <p class="mb-0">Failed to load calendar events. Please refresh the page or contact support.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Booking Modal -->
    <div class="modal fade" id="quickBookingModal" tabindex="-1" aria-labelledby="quickBookingModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="quickBookingModalLabel">Quick Booking</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quickBookingForm">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-xl-12">
                                <label class="form-label">Field</label>
                                <select id="quickField" name="field_id" required class="form-select">
                                    <option value="">Select Field</option>
                                    @foreach ($fields as $field)
                                        <option value="{{ $field->id }}" data-rate="{{ $field->hourly_rate }}">
                                            {{ $field->name }} - ${{ number_format($field->hourly_rate, 2) }}/hr</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-xl-12">
                                <label class="form-label">Date</label>
                                <input type="date" id="quickDate" name="booking_date" required class="form-control">
                            </div>
                            <div class="col-xl-12">
                                <label class="form-label">Start Time</label>
                                <select id="quickTime" name="start_time" required class="form-select">
                                    <option value="">Select Time</option>
                                </select>
                            </div>
                            <div class="col-xl-12">
                                <label class="form-label">Duration (hours)</label>
                                <select id="quickDuration" name="duration_hours" required class="form-select">
                                    <option value="1">1 hour</option>
                                    <option value="2">2 hours</option>
                                    <option value="3">3 hours</option>
                                    <option value="4">4 hours</option>
                                    <option value="5">5 hours</option>
                                    <option value="6">6 hours</option>
                                    <option value="7">7 hours</option>
                                    <option value="8">8 hours</option>
                                </select>
                            </div>
                            <div id="costDisplay" class="col-xl-12 d-none">
                                <div class="alert alert-info">
                                    <strong>Total Cost: $<span id="totalCost">0.00</span></strong>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="quickBookingForm" class="btn btn-primary" id="createBookingBtn">
                        <span class="btn-text">Create Reservation</span>
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                    </button>
                    <button type="button" class="btn btn-info" onclick="redirectToFullForm()" id="fullFormBtn">
                        <i class="ti ti-external-link me-1"></i>Full Form
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- FullCalendar CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const fieldFilter = document.getElementById('fieldFilter');

            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                timeZone: 'local',
                eventDisplay: 'block',
                displayEventTime: true,
                // eventDrop: handleEventDrop,
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                editable: true,
                events: function(fetchInfo, successCallback, failureCallback) {
                    const fieldId = fieldFilter.value;
                    const url = new URL('{{ route('calendar.events') }}');
                    url.searchParams.append('start', fetchInfo.startStr);
                    url.searchParams.append('end', fetchInfo.endStr);
                    if (fieldId) {
                        url.searchParams.append('field_id', fieldId);
                    }

                    console.log('Fetching calendar events from:', url.toString());

                    fetch(url)
                        .then(response => {
                            console.log('Calendar events response status:', response.status);
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar events data received:', data);
                            document.getElementById('calendarLoading').style.display = 'none';
                            document.getElementById('calendar').style.display = 'block';
                            document.getElementById('calendarError').classList.add('d-none');
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Calendar events fetch error:', error);
                            document.getElementById('calendarLoading').style.display = 'none';
                            document.getElementById('calendarError').classList.remove('d-none');
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    // Prevent default navigation
                    info.jsEvent.preventDefault();

                    // Open booking details in new tab/window
                    window.open(info.event.url, '_blank');
                },
                dateClick: function(info) {
                    // Open quick booking modal
                    openQuickBookingModal(info.dateStr);
                },
                eventDidMount: function(info) {
                    // Add tooltip with booking details
                    const props = info.event.extendedProps;
                    info.el.title =
                        `${props.field_name}\nCustomer: ${props.customer_name}\nStatus: ${props.status}\nCost: $${props.total_cost}\nDuration: ${props.duration} hours`;
                }
            });

            // Show loading state initially
            document.getElementById('calendarLoading').style.display = 'block';
            document.getElementById('calendar').style.display = 'none';

            calendar.render();

            // Initial load complete
            setTimeout(() => {
                document.getElementById('calendarLoading').style.display = 'none';
                document.getElementById('calendar').style.display = 'block';
            }, 1000);

            // Refresh calendar when field filter changes
            fieldFilter.addEventListener('change', function() {
                document.getElementById('calendarLoading').style.display = 'block';
                document.getElementById('calendar').style.display = 'none';
                calendar.refetchEvents();
            });

            // Quick booking functionality
            window.openQuickBookingModal = function(date) {
                document.getElementById('quickDate').value = date;
                const modal = new bootstrap.Modal(document.getElementById('quickBookingModal'));
                modal.show();
                loadAvailableSlots();
            };

            window.closeQuickBookingModal = function() {
                const modal = bootstrap.Modal.getInstance(document.getElementById('quickBookingModal'));
                if (modal) modal.hide();
                document.getElementById('quickBookingForm').reset();
                document.getElementById('costDisplay').classList.add('d-none');
            };

            // Load available time slots
            function loadAvailableSlots() {
                const fieldId = document.getElementById('quickField').value;
                const date = document.getElementById('quickDate').value;
                const timeSelect = document.getElementById('quickTime');

                if (!fieldId || !date) {
                    timeSelect.innerHTML = '<option value="">Select Time</option>';
                    return;
                }

                fetch(`{{ route('calendar.events') }}?field_id=${fieldId}&date=${date}`)
                    .then(response => response.json())
                    .then(data => {
                        // Generate available slots (simplified for demo)
                        timeSelect.innerHTML = '<option value="">Select Time</option>';
                        for (let hour = 8; hour < 22; hour++) {
                            const time = String(hour).padStart(2, '0') + ':00';
                            const display = new Date(`2000-01-01T${time}`).toLocaleTimeString([], {
                                hour: 'numeric',
                                minute: '2-digit'
                            });
                            timeSelect.innerHTML += `<option value="${time}">${display}</option>`;
                        }
                    });
            }

            // Calculate cost when duration or field changes
            function calculateCost() {
                const fieldSelect = document.getElementById('quickField');
                const duration = document.getElementById('quickDuration').value;
                const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];

                if (selectedOption && duration) {
                    const rate = parseFloat(selectedOption.dataset.rate || 0);
                    const total = rate * parseInt(duration);
                    document.getElementById('totalCost').textContent = total.toFixed(2);
                    document.getElementById('costDisplay').classList.remove('d-none');
                } else {
                    document.getElementById('costDisplay').classList.add('d-none');
                }
            }

            // Event listeners for cost calculation
            document.getElementById('quickField').addEventListener('change', function() {
                loadAvailableSlots();
                calculateCost();
            });
            document.getElementById('quickDate').addEventListener('change', loadAvailableSlots);
            document.getElementById('quickDuration').addEventListener('change', calculateCost);

            // Handle quick booking form submission
            document.getElementById('quickBookingForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitQuickBooking();
            });

            // Function to redirect to full form
            window.redirectToFullForm = function() {
                const formData = new FormData(document.getElementById('quickBookingForm'));
                const params = new URLSearchParams();
                for (const [key, value] of formData.entries()) {
                    params.append(key, value);
                }

                // Redirect to FPMP reservation form with pre-filled data
                window.location.href = `{{ route('reservations.create') }}?${params.toString()}`;
            };

            // Function to submit booking via AJAX
            function submitQuickBooking() {
                const form = document.getElementById('quickBookingForm');
                const submitBtn = document.getElementById('createBookingBtn');
                const btnText = submitBtn.querySelector('.btn-text');
                const spinner = submitBtn.querySelector('.spinner-border');

                // Validate form
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Show loading state
                submitBtn.disabled = true;
                btnText.textContent = 'Creating...';
                spinner.classList.remove('d-none');

                const formData = new FormData(form);

                fetch('{{ route('reservations.store') }}', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        }
                    })
                    .then(response => {
                        if (response.redirected) {
                            // Success - redirect to the reservation details page
                            window.location.href = response.url;
                            return;
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.errors) {
                            // Handle validation errors
                            showQuickBookingErrors(data.errors);
                        }
                    })
                    .catch(error => {
                        console.error('Error creating reservation:', error);
                        showQuickBookingError(
                            'An error occurred while creating the reservation. Please try again.');
                    })
                    .finally(() => {
                        // Reset loading state
                        submitBtn.disabled = false;
                        btnText.textContent = 'Create Reservation';
                        spinner.classList.add('d-none');
                    });
            }

            // Function to show errors in the modal
            function showQuickBookingErrors(errors) {
                // Remove existing error messages
                document.querySelectorAll('.quick-booking-error').forEach(el => el.remove());

                // Add error messages
                Object.keys(errors).forEach(field => {
                    const fieldElement = document.getElementById('quick' + field.charAt(0).toUpperCase() +
                        field.slice(1));
                    if (fieldElement) {
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'text-danger fs-12 mt-1 quick-booking-error';
                        errorDiv.textContent = errors[field][0];
                        fieldElement.parentNode.appendChild(errorDiv);
                    }
                });
            }

            // Function to show general error
            function showQuickBookingError(message) {
                // Remove existing error messages
                document.querySelectorAll('.quick-booking-error').forEach(el => el.remove());

                // Add general error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger quick-booking-error';
                errorDiv.textContent = message;
                document.querySelector('#quickBookingModal .modal-body').prepend(errorDiv);
            }

            // Reset form when modal is hidden
            document.getElementById('quickBookingModal').addEventListener('hidden.bs.modal', function() {
                document.getElementById('quickBookingForm').reset();
                document.getElementById('costDisplay').classList.add('d-none');
                // Clear any error messages
                document.querySelectorAll('.quick-booking-error').forEach(el => el.remove());
                // Reset button state
                const submitBtn = document.getElementById('createBookingBtn');
                const btnText = submitBtn.querySelector('.btn-text');
                const spinner = submitBtn.querySelector('.spinner-border');
                submitBtn.disabled = false;
                btnText.textContent = 'Create Reservation';
                spinner.classList.add('d-none');
            });
        });
    </script>
@endsection
