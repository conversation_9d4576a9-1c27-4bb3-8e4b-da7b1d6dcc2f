@extends('layouts.admin')

@section('title', 'Create New Booking - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Create New Booking</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('bookings.index') }}">Bookings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Create</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Create Booking Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Booking Information</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('calendar.index') }}" class="btn btn-info btn-sm">
                            <i class="ti ti-calendar me-1"></i>Calendar
                        </a>
                        <a href="{{ route('bookings.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Bookings
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('bookings.store') }}" id="bookingForm">
                        @csrf

                        <div class="row gy-4">
                            <!-- Field and Date Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Field & Schedule</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); checkAvailability();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @if (isset($fields))
                                                        @foreach ($fields as $field)
                                                            <option value="{{ $field->id }}"
                                                                data-rate="{{ $field->hourly_rate }}"
                                                                data-capacity="{{ $field->capacity }}"
                                                                data-type="{{ $field->type }}"
                                                                {{ request('field_id') == $field->id || (isset($selectedField) && $selectedField->id == $field->id) ? 'selected' : '' }}>
                                                                {{ $field->name }} -
                                                                ${{ number_format($field->hourly_rate, 2) }}/hr
                                                            </option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo" class="alert alert-info d-none">
                                                    <h6 class="fw-semibold">Field Information</h6>
                                                    <p class="mb-1"><strong>Type:</strong> <span id="fieldType"></span>
                                                    </p>
                                                    <p class="mb-1"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity"></span> people</p>
                                                    <p class="mb-0"><strong>Hourly Rate:</strong> $<span
                                                            id="fieldRate"></span></p>
                                                </div>
                                            </div>

                                            <!-- Booking Date -->
                                            <div class="col-xl-12">
                                                <label for="booking_date" class="form-label">Date <span
                                                        class="text-danger">*</span></label>
                                                <input type="date" name="booking_date" id="booking_date"
                                                    value="{{ old('booking_date', $selectedDate ?? '') }}"
                                                    min="{{ date('Y-m-d') }}" required onchange="checkAvailability();"
                                                    class="form-control @error('booking_date') is-invalid @enderror">
                                                @error('booking_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Start Time -->
                                            <div class="col-xl-12">
                                                <label for="start_time" class="form-label">Start Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required
                                                    onchange="checkAvailability();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Time</option>
                                                    @for ($hour = 8; $hour < 22; $hour++)
                                                        @php
                                                            $time = sprintf('%02d:00', $hour);
                                                            $display = date('g:i A', strtotime($time));
                                                        @endphp
                                                        <option value="{{ $time }}"
                                                            {{ old('start_time', $selectedTime ?? '') == $time ? 'selected' : '' }}>
                                                            {{ $display }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Duration -->
                                            <div class="col-xl-12">
                                                <label for="duration_hours" class="form-label">Duration <span
                                                        class="text-danger">*</span></label>
                                                <select name="duration_hours" id="duration_hours" required
                                                    onchange="calculateCost(); checkAvailability();"
                                                    class="form-select @error('duration_hours') is-invalid @enderror">
                                                    @for ($i = 1; $i <= 8; $i++)
                                                        <option value="{{ $i }}"
                                                            {{ old('duration_hours', 1) == $i ? 'selected' : '' }}>
                                                            {{ $i }} {{ Str::plural('hour', $i) }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('duration_hours')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Availability Check -->
                                            <div class="col-xl-12">
                                                <div id="availabilityCheck" class="d-none">
                                                    <div id="availabilityMessage" class="alert"></div>
                                                </div>
                                            </div>

                                            <!-- Cost Display -->
                                            <div class="col-xl-12">
                                                <div id="costDisplay" class="alert alert-success d-none">
                                                    <h6 class="fw-semibold">Booking Cost</h6>
                                                    <p class="mb-1"><strong>Total Cost: $<span
                                                                id="totalCost">0.00</span></strong></p>
                                                    <p class="mb-0 fs-12">Rate: $<span id="displayRate">0.00</span>/hour ×
                                                        <span id="displayDuration">1</span> hour(s)
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        @if (auth()->user()->isAdmin())
                                            <div class="alert alert-warning">
                                                <strong>Admin Note:</strong> You can book for other customers. Leave
                                                customer fields empty to book for yourself.
                                            </div>
                                        @endif

                                        <div class="row gy-3">
                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">
                                                    Customer Name
                                                    @if (!auth()->user()->isAdmin())
                                                        <span class="text-muted">(Optional)</span>
                                                    @endif
                                                </label>
                                                <input type="text" name="customer_name" id="customer_name"
                                                    value="{{ old('customer_name') }}"
                                                    placeholder="{{ auth()->user()->isAdmin() ? 'Enter customer name or leave empty for yourself' : 'Leave empty to use your name' }}"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email') }}"
                                                    placeholder="{{ auth()->user()->isAdmin() ? 'Customer email address' : 'Leave empty to use your email' }}"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone</label>
                                                <input type="tel" name="customer_phone" id="customer_phone"
                                                    value="{{ old('customer_phone') }}"
                                                    placeholder="Phone number (optional)"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests') }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Booking Rules -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-info">
                                                    <h6 class="fw-semibold">Booking Rules</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li> Bookings are available from 8:00 AM to 10:00 PM</li>
                                                        <li> Minimum booking duration: 1 hour</li>
                                                        <li> Maximum booking duration: 8 hours</li>
                                                        @if (auth()->user()->isClient())
                                                            <li> Client bookings require admin approval</li>
                                                        @endif
                                                        <li> Cancellations must be made before the booking date</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-end mt-4 pt-3 border-top">
                                    <a href="{{ route('bookings.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>Cancel
                                    </a>
                                    <button type="submit" id="submitBtn" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>Create Booking
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script>
            function updateFieldInfo() {
                const fieldSelect = document.getElementById('field_id');
                const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
                const fieldInfo = document.getElementById('fieldInfo');

                if (selectedOption.value) {
                    document.getElementById('fieldType').textContent = selectedOption.dataset.type || 'N/A';
                    document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';
                    document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                    fieldInfo.classList.remove('d-none');
                    calculateCost();
                } else {
                    fieldInfo.classList.add('d-none');
                    document.getElementById('costDisplay').classList.add('d-none');
                }
            }

            function calculateCost() {
                const fieldSelect = document.getElementById('field_id');
                const duration = document.getElementById('duration_hours').value;
                const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];

                if (selectedOption.value && duration) {
                    const rate = parseFloat(selectedOption.dataset.rate || 0);
                    const total = rate * parseInt(duration);

                    document.getElementById('totalCost').textContent = total.toFixed(2);
                    document.getElementById('displayRate').textContent = rate.toFixed(2);
                    document.getElementById('displayDuration').textContent = duration;
                    document.getElementById('costDisplay').classList.remove('d-none');
                } else {
                    document.getElementById('costDisplay').classList.add('d-none');
                }
            }

            function checkAvailability() {
                const fieldId = document.getElementById('field_id').value;
                const date = document.getElementById('booking_date').value;
                const startTime = document.getElementById('start_time').value;
                const duration = document.getElementById('duration_hours').value;

                const availabilityCheck = document.getElementById('availabilityCheck');
                const availabilityMessage = document.getElementById('availabilityMessage');
                const submitBtn = document.getElementById('submitBtn');

                if (!fieldId || !date || !startTime || !duration) {
                    availabilityCheck.classList.add('d-none');
                    return;
                }

                // Show checking message
                availabilityMessage.className = 'alert alert-warning';
                availabilityMessage.textContent = 'Checking availability...';
                availabilityCheck.classList.remove('d-none');

                // For demo purposes, we'll assume it's available
                // In a real application, you would make an AJAX call to check availability
                setTimeout(() => {
                    availabilityMessage.className = 'alert alert-success';
                    availabilityMessage.textContent = '✓ Time slot is available';
                    submitBtn.disabled = false;
                }, 1000);
            }

            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                updateFieldInfo();
                calculateCost();
                checkAvailability();
            });
        </script>
    @endpush

@endsection
